from flask import Flask, render_template, request, jsonify
from notifications import send_email, send_sms
import sqlite3

app = Flask(__name__)

def init_db():
    conn = sqlite3.connect('renewals.db')
    c = conn.cursor()
    c.execute('''CREATE TABLE IF NOT EXISTS renewals (
        id INTEGER PRIMARY KEY,
        user TEXT,
        type TEXT,
        email TEXT,
        phone TEXT
    )''')
    conn.commit()
    conn.close()

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/renew', methods=['POST'])
def renew():
    data = request.form
    user, rtype, email, phone = data['user'], data['type'], data['email'], data['phone']
    conn = sqlite3.connect('renewals.db')
    c = conn.cursor()
    c.execute("INSERT INTO renewals (user, type, email, phone) VALUES (?, ?, ?, ?)", (user, rtype, email, phone))
    conn.commit()
    conn.close()

    send_email(email, f'{rtype.upper()} Renewal Confirmation', f'Hello {user}, your {rtype.upper()} renewal has been received.')
    send_sms(phone, f'{rtype.upper()} Renewal processed for {user}')
    return render_template('index.html', message="Renewal submitted!")

@app.route('/api/renew/<rtype>', methods=['POST'])
def api_renew(rtype):
    data = request.json
    user, email, phone = data['user'], data['email'], data['phone']
    conn = sqlite3.connect('renewals.db')
    c = conn.cursor()
    c.execute("INSERT INTO renewals (user, type, email, phone) VALUES (?, ?, ?, ?)", (user, rtype, email, phone))
    conn.commit()
    conn.close()

    send_email(email, f'{rtype.upper()} Renewal Confirmation', f'Hello {user}, your {rtype.upper()} renewal has been received.')
    send_sms(phone, f'{rtype.upper()} Renewal processed for {user}')
    return jsonify({"status": "success"}), 200

if __name__ == '__main__':
    init_db()
    app.run(debug=True)